import _ from 'lodash';
import {trim} from 'framework/helpers';
import {getPartnerAccounts} from '../../../accounting/methods/partner-ledger';

export default async function (app, store, request, response) {
    const customerId = trim(request.body.customerId || '');
    if (!customerId) {
        return response.status(422).json({
            status: 'error',
            code: 'customer_id_is_required',
            message: 'Customer ID is required'
        });
    }

    const {skip = 0, limit = 50} = request.body;

    const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances: false});
    // let accountIds = await app.cache.get('distinct-partner-accounting-accounts');
    // if (!accountIds) {
    //     const partnerAccounts = await app.collection('kernel.accounts').find({
    //         isPartnerAccount: true,
    //         $select: ['_id']
    //     });
    //     if (partnerAccounts.length > 0) {
    //         accountIds = partnerAccounts.map(account => account._id);
    //     } else {
    //         accountIds = await app.collection('kernel.partners').distinct('accountingAccountId');
    //     }
    //
    //     await app.cache.set('distinct-partner-accounting-accounts', accountIds, 60 * 30);
    // }

    const result = {
        debit: 0,
        credit: 0,
        balance: 0,
        debitFC: 0,
        creditFC: 0,
        balanceFC: 0,
        transactions: []
    };

    let balanceReport = await app.collection('accounting.transactions').aggregate([
        {
            $match: {
                partnerId: customerId,
                accountId: {$in: accountIds}
            }
        },
        {
            $group: {
                _id: null,
                debit: {$sum: '$debit'},
                credit: {$sum: '$credit'},
                debitFC: {$sum: '$debitFC'},
                creditFC: {$sum: '$creditFC'}
            }
        },
        {$project: {
            debit: 1,
            credit: 1,
            balance: {$subtract: ['$debit', '$credit']},
            debitFC: 1,
            creditFC: 1,
            balanceFC: {$subtract: ['$debitFC', '$creditFC']}
        }}
    ]);
    if (balanceReport.length > 0) {
        const r = balanceReport[0];

        result.debit = r.debit;
        result.credit = r.credit;
        result.balance = r.balance;
        result.debitFC = r.debitFC;
        result.creditFC = r.creditFC;
        result.balanceFC  = r.balanceFC;
    }

    const transactions = await app.collection('accounting.transactions').find({
        partnerId: customerId,
        accountId: {$in: accountIds},
        $skip: skip,
        $limit: limit,
        $sort: {issueDate: -1}
    });

    let initialBalance = 0;
    if (transactions.length > 0) {
        initialBalance = await app.rpc('accounting.get-last-balance-before-transaction', {
            transactionId: transactions[0]._id,
            issueDate: transactions[0].issueDate
        });
    }

    for (const transaction of transactions) {
        const row = {};

        row.id = transaction._id;
        row.issueDate = transaction.issueDate;
        row.dueDate = transaction.dueDate;
        row.documentNo = transaction.documentNo;
        row.currency = transaction.currency.name;
        row.reference = transaction.reference ?? '';
        row.description = transaction.description ?? '';
        row.tinIdentity = transaction.partner.tinIdentity;
        row.projectCode = '';
        row.projectName = '';
        row.initialBalance = initialBalance;
        row.debit = transaction.debit;
        row.credit = transaction.credit;
        row.balance = row.initialBalance + (transaction.debit - transaction.credit);

        // Yabancı para birimi alanları
        row.debitFC = transaction.debitFC || 0;
        row.creditFC = transaction.creditFC || 0;
        row.balanceFC = (row.debitFC - row.creditFC) + (row.balanceFC || 0);
        // Eğer initialYpbBalance tutulacaksa, benzer şekilde initialYpbBalance hesaplanabilir.

        if (!!transaction.financialProject) {
            row.projectCode = transaction.financialProject.code;
            row.projectName = transaction.financialProject.name;
        }

        initialBalance = row.balance;

        result.transactions.push(row);
    }

    return response.send(result);
}
